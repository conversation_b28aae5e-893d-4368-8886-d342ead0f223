import React, { useState } from "react";
import { format, addDays, startOfWeek, addWeeks } from "date-fns";
import rightArrow from "@/assets/svgs/rightArrow.svg";
import leftArrow from "@/assets/svgs/leftArrow.svg";
import tutorImage from "@/assets/images/tutor1.png";
import { Button } from "@/components/button/button";
import { useNavigate } from "react-router-dom";
import RescheduleModal from "./components/TutorRescheduleModal";
import ConfirmationModal from "./components/ConfirmationModal";
import ScheduleTutorModal from "./components/ScheduleTutorModal";
import { useGetTutorClassesQuery } from "@/redux/slices/student/classesApiSlice";
import { useCancelClassBookingMutation } from "@/redux/slices/student/scheduleApiSlice";
import useGet from "@/hooks/useGet";
import TutuorLessonInfoModal from "./components/TutuorLessonInfoModal";
import TutorRescheduleModal from "./components/TutorRescheduleModal";
import { useSelector } from "react-redux";

const DAYS = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

export default function Calendar() {
	const [hoveredSlot, setHoveredSlot] = useState(null);
	const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
	const [weekOffset, setWeekOffset] = useState(0);
	const [slotDuration, setSlotDuration] = useState(25);
	const [selectedLesson, setSelectedLesson] = useState(null);
	const [showReschedule, setShowReschedule] = useState(false);
	const [showCancelConfirm, setShowCancelConfirm] = useState(false);
	const [showSchedulePopup, setShowSchedulePopup] = useState(false);
	const [selectedSlot, setSelectedSlot] = useState(null);

	// Fetch bookings data
	const { data: bookings, isLoading } = useGet(useGetTutorClassesQuery, "");
	const [cancelBooking, { isLoading: isCancelling }] =
		useCancelClassBookingMutation();

	// cancel student booking
	const onCancelBookingConfirm = async ({ reason }) => {
		try {
			const bookingId = selectedLesson?.id;
			if (!bookingId) return;

			await cancelBooking({ id: bookingId, reason }).unwrap();
			closePopup(); // hide modal and reset state
		} catch (error) {
			console.error("Cancel failed:", error);
			alert("Failed to cancel. Please try again.");
		}
	};

	const HOURS = Array.from({ length: 24 }, (_, i) => i); // 0-23 for 24-hour format
	const SLOTS_PER_HOUR = 60 / slotDuration;
	const slotHeight = slotDuration === 25 ? 35 : 70;

	const startDate = addWeeks(
		startOfWeek(new Date(), { weekStartsOn: 0 }),
		weekOffset
	);

	const endDate = addDays(startDate, 6);

	const getLessonsForWeek = () => {
		if (!bookings?.bookings) return [];

		return bookings.bookings
			.filter((booking) => {
				const bookingDate = new Date(booking.scheduledTime);
				return bookingDate >= startDate && bookingDate <= endDate;
			})
			.map((booking) => {
				const bookingDate = new Date(booking.scheduledTime);
				const day = bookingDate.getDay(); // 0 (Sun) to 6 (Sat)
				const hour = bookingDate.getHours();
				const minutes = bookingDate.getMinutes();
				const status = booking.status;

				return {
					id: booking.id,
					day,
					time: hour,
					minutes,
					status,
					duration: booking.duration,
					title: booking.title || "Tutoring Session",
					tutor: booking.tutor?.name || "Tutor",
					// date: bookingDate,
					date: format(bookingDate, "yyyy-MM-dd"), // Format as string instead of Date object
					formattedTime: format(bookingDate, "h:mm a"),
					endTime: new Date(bookingDate.getTime() + booking.duration * 60000),
					bookingData: booking,
				};
			});
	};

	const lessons = getLessonsForWeek();

	const formatRange = `${format(startDate, "MMM d")} – ${format(
		endDate,
		"d, yyyy"
	)}`;

	const handleMouseEnter = (dayIdx, hour, slotIdx) => {
		const key = `${dayIdx}-${hour}-${slotIdx}`;
		setHoveredSlot({ key, dayIdx, hour, slotIdx });
	};

	const handleMouseLeave = () => setHoveredSlot(null);

	const getHoverDateTime = () => {
		if (!hoveredSlot) return {};
		const { dayIdx, hour, slotIdx } = hoveredSlot;
		const date = addDays(startDate, dayIdx);

		const startTime = new Date(date);
		startTime.setHours(hour, slotIdx * slotDuration);

		const endTime = new Date(startTime);
		endTime.setMinutes(endTime.getMinutes() + slotDuration);

		return {
			dateLabel: format(date, "EEEE, MMM d, yyyy"),
			timeLabel: `${format(startTime, "h:mm a")} – ${format(
				endTime,
				"h:mm a"
			)}`,
		};
	};

	const { dateLabel, timeLabel } = getHoverDateTime();

	const handleLessonClick = (lesson) => {
		setSelectedLesson(lesson);
	};

	const handleReschedule = () => {
		setShowReschedule(true);
	};
	const handleScheduleStudent = (student) => {
		// Handle scheduling logic with the selected student
		console.log("Scheduling with:", student);
		setShowSchedulePopup(false);
	};

	const handleCancel = () => {
		setShowCancelConfirm(true);
	};

	const handleContinue = () => {
		// continue logic
		closePopup();
	};

	const handleSchedule = (dayIdx, hour, slotIdx) => {
		const date = addDays(startDate, dayIdx);
		const startTime = new Date(date);
		startTime.setHours(hour, slotIdx * slotDuration);
		const endTime = new Date(startTime);
		endTime.setMinutes(endTime.getMinutes() + slotDuration);
		setSelectedSlot({
			dayIdx,
			hour,
			slotIdx,
			dateLabel: format(date, "EEEE, MMM d, yyyy"),
			timeLabel: `${format(startTime, "h:mm a")} – ${format(
				endTime,
				"h:mm a"
			)}`,
		});
		setShowSchedulePopup(true);
	};

	const closeReschedule = () => {
		setShowReschedule(false);
	};
	const closePopup = () => {
		setSelectedLesson(null);
		setShowReschedule(false);
		setShowCancelConfirm(false);
		setShowSchedulePopup(false);
		setSelectedSlot(null);
		setHoveredSlot(null);
	};

	console.log(selectedLesson);

	return (
		<div className="w-full relative">
			<div className="flex items-center gap-2 mb-7">
				<p className="border border-[#E8E8E8] p-[6px] rounded-lg px-5">Today</p>
				<button
					onClick={() => setWeekOffset((w) => w - 1)}
					className="bg-white border border-[#E8E8E8] p-[6px] rounded-lg"
				>
					<img src={leftArrow} alt="prev" />
				</button>
				<button
					onClick={() => setWeekOffset((w) => w + 1)}
					className="bg-white border border-[#E8E8E8] p-[6px] rounded-lg"
				>
					<img src={rightArrow} alt="next" />
				</button>
				<p className="sm:text-xl text-lg font-bold">{formatRange}</p>
			</div>

			{/* Header */}
			<div className="grid grid-cols-8 text-sm font-medium border-b">
				<div></div>
				{DAYS.map((_, i) => (
					<div key={i} className="pl-2 py-2 border-l">
						<p className="text-[#71717A]">
							{format(addDays(startDate, i), "EEE")}
						</p>
						<p className="text-black">{format(addDays(startDate, i), "dd")}</p>
					</div>
				))}
			</div>

			{/* Body */}
			<div className="grid grid-cols-8">
				{/* Time Labels */}
				<div className="flex flex-col">
					{HOURS.flatMap((hour) =>
						Array.from({ length: SLOTS_PER_HOUR }).map((_, slotIdx) => (
							<div
								key={`${hour}-${slotIdx}`}
								className="text-xs text-gray-600 flex items-start"
								style={{ height: slotHeight }}
							>
								{slotIdx === 0 && `${hour}:00`}{" "}
								{/* Display as "0:00" to "23:00" */}
							</div>
						))
					)}
				</div>

				{/* Slots */}
				{DAYS.map((_, dayIdx) => (
					<div key={dayIdx} className="flex flex-col border-l relative">
						{HOURS.flatMap((hour) =>
							Array.from({ length: SLOTS_PER_HOUR }).map((_, slotIdx) => {
								const key = `${dayIdx}-${hour}-${slotIdx}`;
								const isHovering = hoveredSlot?.key === key;
								const lesson = lessons.find(
									(l) => l.day === dayIdx && l.time === hour
								);

								return (
									<div
										key={key}
										className={`relative border-b h-[1px] ${
											lesson && slotIdx === 0
												? "bg-[#0EA5E91A] border-l-2 border-l-[#0EA5E9]"
												: ""
										}`}
										style={{ height: slotHeight }}
										onMouseEnter={() => handleMouseEnter(dayIdx, hour, slotIdx)}
										onMouseLeave={handleMouseLeave}
										onMouseMove={(e) =>
											setMousePosition({ x: e.clientX + 10, y: e.clientY + 10 })
										}
										onClick={() => {
											if (lesson) {
												handleLessonClick(lesson);
											} else {
												handleSchedule(dayIdx, hour, slotIdx);
											}
										}}
									>
										{/* Existing lesson */}
										{lesson && slotIdx === 0 && (
											<div className="absolute inset-1 bg-sky-100 text-xs text-[#0369A1] rounded whitespace-pre-line z-10">
												{lesson.title}
											</div>
										)}
									</div>
								);
							})
						)}
					</div>
				))}
			</div>

			{/* Lesson Information Popup */}
			{selectedLesson && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
					<TutuorLessonInfoModal
						selectedLesson={selectedLesson}
						onClose={closePopup}
						onReschedule={handleReschedule}
						onCancel={handleCancel}
					/>{" "}
				</div>
			)}

			{/* Reschedule Popup */}
			{showReschedule && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
					<TutorRescheduleModal
						show={showReschedule}
						lesson={selectedLesson}
						onClose={closeReschedule}
						onContinue={handleContinue}
					/>
				</div>
			)}

			{/* Cancel Confirmation Popup */}
			<div>
				<ConfirmationModal
					show={showCancelConfirm}
					title="Are you sure you want to cancel lesson?"
					confirmText="Yes, cancel"
					onConfirm={handleCancel}
					onCancel={() => setShowCancelConfirm(false)}
					confirmColor="red" // Optional: makes the button red
				/>
			</div>
		</div>
	);
}
