import React, { useState, useMemo } from "react";
import { format, addDays, startOfWeek, addWeeks, isSameDay, startOfMonth, endOfMonth, addMonths, eachDayOfInterval, isToday, isSameMonth } from "date-fns";
import rightArrow from "@/assets/svgs/rightArrow.svg";
import leftArrow from "@/assets/svgs/leftArrow.svg";
import LessonInfoModal from "./components/LessonInfoModal";
import RescheduleModal from "./components/RescheduleModal";
import CancelConfirmationModal from "./components/CancelConfirmationModal";
import ScheduleTutorModal from "./components/ScheduleTutorModal";
import useGet from "@/hooks/useGet";
import { useGetClassesQuery } from "@/redux/slices/student/classesApiSlice";
import { useCancelClassBookingMutation } from "@/redux/slices/student/scheduleApiSlice";
import userVector from "@/assets/svgs/userVector.svg";
import { useSelector } from "react-redux";

const DAYS = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
const VIEW_TYPES = {
	WEEK: 'week',
	MONTH: 'month',
	LIST: 'list'
};

const STATUS_COLORS = {
	scheduled: { bg: 'bg-blue-100', text: 'text-blue-800', border: 'border-blue-200' },
	confirmed: { bg: 'bg-green-100', text: 'text-green-800', border: 'border-green-200' },
	completed: { bg: 'bg-gray-100', text: 'text-gray-800', border: 'border-gray-200' },
	cancelled: { bg: 'bg-red-100', text: 'text-red-800', border: 'border-red-200' },
	pending: { bg: 'bg-yellow-100', text: 'text-yellow-800', border: 'border-yellow-200' }
};

export default function Calendar() {
	// View and navigation state
	const [currentView, setCurrentView] = useState(VIEW_TYPES.WEEK);
	const [weekOffset, setWeekOffset] = useState(0);
	const [monthOffset, setMonthOffset] = useState(0);
	const [slotDuration] = useState(25);

	// Modal and interaction state
	const [selectedLesson, setSelectedLesson] = useState(null);
	const [showReschedule, setShowReschedule] = useState(false);
	const [showCancelConfirm, setShowCancelConfirm] = useState(false);
	const [showSchedulePopup, setShowSchedulePopup] = useState(false);
	const [selectedSlot, setSelectedSlot] = useState(null);

	// Filter and sort state
	const [statusFilter, setStatusFilter] = useState('all');
	const [tutorFilter, setTutorFilter] = useState('all');
	const [sortBy, setSortBy] = useState('date'); // date, tutor, status
	const [sortOrder, setSortOrder] = useState('asc'); // asc, desc

	// Get current user info
	const user = useSelector((state) => state?.app?.userInfo?.user);
	const isStudent = user?.role === "student";

	// Fetch bookings data
	const { data: bookings } = useGet(useGetClassesQuery, "", isStudent);
	const [cancelBooking] = useCancelClassBookingMutation();

	// cancel student booking
	const onCancelBookingConfirm = async ({ reason }) => {
		try {
			const bookingId = selectedLesson?.id;
			if (!bookingId) return;

			await cancelBooking({ id: bookingId, reason }).unwrap();
			closePopup(); // hide modal and reset state
		} catch (error) {
			console.error("Cancel failed:", error);
			alert("Failed to cancel. Please try again.");
		}
	};

	const HOURS = Array.from({ length: 24 }, (_, i) => i);
	const SLOTS_PER_HOUR = 60 / slotDuration;
	const slotHeight = slotDuration === 25 ? 35 : 70;

	const startDate = addWeeks(
		startOfWeek(new Date(), { weekStartsOn: 0 }),
		weekOffset
	);

	const endDate = addDays(startDate, 6);

	// Get all lessons with filtering and sorting
	const getAllLessons = useMemo(() => {
		if (!bookings?.bookings) return [];

		return bookings.bookings.map((booking) => {
			const bookingDate = new Date(booking.scheduledTime);
			const day = bookingDate.getDay(); // 0 (Sun) to 6 (Sat)
			const hour = bookingDate.getHours();
			const minutes = bookingDate.getMinutes();
			const status = booking.status;

			return {
				id: booking.id,
				day,
				time: hour,
				minutes,
				status,
				duration: booking.duration,
				title: booking.title || "Tutoring Session",
				tutor: booking.tutor?.name || "Tutor",
				tutorId: booking.tutor?.id || booking.tutorId,
				date: bookingDate,
				dateString: format(bookingDate, "yyyy-MM-dd"),
				formattedDate: format(bookingDate, "MMM d, yyyy"),
				formattedTime: format(bookingDate, "h:mm a"),
				endTime: new Date(bookingDate.getTime() + booking.duration * 60000),
				bookingData: booking,
				image: booking.tutor?.profilePicture || userVector,
				frequency: booking.frequency || "One-time session",
				isFreeTrial: booking.isFreeTrial || false,
			};
		});
	}, [bookings]);

	// Filter and sort lessons
	const filteredAndSortedLessons = useMemo(() => {
		let filtered = getAllLessons;

		// Apply status filter
		if (statusFilter !== 'all') {
			filtered = filtered.filter(lesson => lesson.status === statusFilter);
		}

		// Apply tutor filter
		if (tutorFilter !== 'all') {
			filtered = filtered.filter(lesson => lesson.tutorId === tutorFilter);
		}

		// Sort lessons
		filtered.sort((a, b) => {
			let comparison = 0;

			switch (sortBy) {
				case 'date':
					comparison = a.date.getTime() - b.date.getTime();
					break;
				case 'tutor':
					comparison = a.tutor.localeCompare(b.tutor);
					break;
				case 'status':
					comparison = a.status.localeCompare(b.status);
					break;
				default:
					comparison = a.date.getTime() - b.date.getTime();
			}

			return sortOrder === 'asc' ? comparison : -comparison;
		});

		return filtered;
	}, [getAllLessons, statusFilter, tutorFilter, sortBy, sortOrder]);

	// Get lessons for current view period
	const getLessonsForPeriod = () => {
		const currentDate = new Date();
		let startPeriod, endPeriod;

		if (currentView === VIEW_TYPES.WEEK) {
			startPeriod = addWeeks(startOfWeek(currentDate, { weekStartsOn: 0 }), weekOffset);
			endPeriod = addDays(startPeriod, 6);
		} else if (currentView === VIEW_TYPES.MONTH) {
			const monthStart = addMonths(startOfMonth(currentDate), monthOffset);
			startPeriod = startOfMonth(monthStart);
			endPeriod = endOfMonth(monthStart);
		} else {
			// For list view, return all filtered lessons
			return filteredAndSortedLessons;
		}

		return filteredAndSortedLessons.filter((lesson) => {
			return lesson.date >= startPeriod && lesson.date <= endPeriod;
		});
	};

	const lessons = getLessonsForPeriod();

	// Get unique tutors for filter dropdown
	const uniqueTutors = useMemo(() => {
		const tutors = new Set();
		getAllLessons.forEach(lesson => {
			if (lesson.tutorId && lesson.tutor) {
				tutors.add(JSON.stringify({ id: lesson.tutorId, name: lesson.tutor }));
			}
		});
		return Array.from(tutors).map(tutor => JSON.parse(tutor));
	}, [getAllLessons]);

	// Date range formatting
	const getDateRangeText = () => {
		const currentDate = new Date();

		if (currentView === VIEW_TYPES.WEEK) {
			const startDate = addWeeks(startOfWeek(currentDate, { weekStartsOn: 0 }), weekOffset);
			const endDate = addDays(startDate, 6);
			return `${format(startDate, "MMM d")} – ${format(endDate, "d, yyyy")}`;
		} else if (currentView === VIEW_TYPES.MONTH) {
			const monthStart = addMonths(startOfMonth(currentDate), monthOffset);
			return format(monthStart, "MMMM yyyy");
		} else {
			return "All Classes";
		}
	};

	// Navigation handlers
	const handlePrevious = () => {
		if (currentView === VIEW_TYPES.WEEK) {
			setWeekOffset(w => w - 1);
		} else if (currentView === VIEW_TYPES.MONTH) {
			setMonthOffset(m => m - 1);
		}
	};

	const handleNext = () => {
		if (currentView === VIEW_TYPES.WEEK) {
			setWeekOffset(w => w + 1);
		} else if (currentView === VIEW_TYPES.MONTH) {
			setMonthOffset(m => m + 1);
		}
	};

	const handleToday = () => {
		setWeekOffset(0);
		setMonthOffset(0);
	};
	// List View Component
	const ListView = () => (
		<div className="space-y-4">
			{lessons.length === 0 ? (
				<div className="text-center py-12 bg-white rounded-lg border">
					<div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
						<svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
						</svg>
					</div>
					<h3 className="text-lg font-medium text-gray-900 mb-2">No classes found</h3>
					<p className="text-gray-500 text-sm max-w-sm mx-auto">
						{statusFilter !== 'all' || tutorFilter !== 'all'
							? 'Try adjusting your filters to see more classes.'
							: 'You haven\'t scheduled any classes yet. Book a lesson with one of your tutors to get started.'
						}
					</p>
					{statusFilter === 'all' && tutorFilter === 'all' && (
						<button
							onClick={() => window.location.href = '/student/find-tutors'}
							className="mt-4 bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary-dark transition-colors duration-200"
						>
							Find Tutors
						</button>
					)}
				</div>
			) : (
				lessons.map((lesson) => {
					const statusStyle = STATUS_COLORS[lesson.status] || STATUS_COLORS.scheduled;
					return (
						<div
							key={lesson.id}
							className={`border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer ${statusStyle.border}`}
							onClick={() => handleLessonClick(lesson)}
						>
							<div className="flex items-center justify-between">
								<div className="flex items-center space-x-4">
									<img
										src={lesson.image}
										alt={lesson.tutor}
										className="w-12 h-12 rounded-full object-cover"
									/>
									<div>
										<h3 className="font-semibold text-gray-900">{lesson.title}</h3>
										<p className="text-sm text-gray-600">with {lesson.tutor}</p>
										<div className="flex items-center space-x-4 mt-1">
											<span className="text-sm text-gray-500">
												{lesson.formattedDate} at {lesson.formattedTime}
											</span>
											<span className="text-sm text-gray-500">
												{lesson.duration} min
											</span>
											{lesson.isFreeTrial && (
												<span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
													Free Trial
												</span>
											)}
										</div>
									</div>
								</div>
								<div className="text-right">
									<span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusStyle.bg} ${statusStyle.text}`}>
										{lesson.status.charAt(0).toUpperCase() + lesson.status.slice(1)}
									</span>
								</div>
							</div>
						</div>
					);
				})
			)}
		</div>
	);

	// Month View Component
	const MonthView = () => {
		const currentDate = new Date();
		const monthStart = addMonths(startOfMonth(currentDate), monthOffset);
		const monthEnd = endOfMonth(monthStart);
		const startDate = startOfWeek(monthStart, { weekStartsOn: 0 });
		const endDate = addDays(startOfWeek(monthEnd, { weekStartsOn: 0 }), 41); // 6 weeks

		const days = eachDayOfInterval({ start: startDate, end: endDate });

		const getLessonsForDay = (day) => {
			return lessons.filter(lesson => isSameDay(lesson.date, day));
		};

		return (
			<div className="bg-white rounded-lg border">
				{/* Month header */}
				<div className="grid grid-cols-7 border-b">
					{DAYS.map((day) => (
						<div key={day} className="p-3 text-center text-sm font-medium text-gray-500 border-r last:border-r-0">
							{day}
						</div>
					))}
				</div>

				{/* Month grid */}
				<div className="grid grid-cols-7">
					{days.map((day, dayIdx) => {
						const dayLessons = getLessonsForDay(day);
						const isCurrentMonth = isSameMonth(day, monthStart);
						const isCurrentDay = isToday(day);

						return (
							<div
								key={day.toISOString()}
								className={`min-h-[120px] p-2 border-r border-b last:border-r-0 ${
									!isCurrentMonth ? 'bg-gray-50' : 'bg-white'
								} ${isCurrentDay ? 'bg-blue-50' : ''}`}
							>
								<div className={`text-sm font-medium mb-1 ${
									!isCurrentMonth ? 'text-gray-400' : isCurrentDay ? 'text-blue-600' : 'text-gray-900'
								}`}>
									{format(day, 'd')}
								</div>
								<div className="space-y-1">
									{dayLessons.slice(0, 3).map((lesson) => {
										const statusStyle = STATUS_COLORS[lesson.status] || STATUS_COLORS.scheduled;
										return (
											<div
												key={lesson.id}
												className={`text-xs p-1 rounded cursor-pointer ${statusStyle.bg} ${statusStyle.text} truncate`}
												onClick={() => handleLessonClick(lesson)}
												title={`${lesson.title} with ${lesson.tutor} at ${lesson.formattedTime}`}
											>
												{lesson.formattedTime} {lesson.tutor}
											</div>
										);
									})}
									{dayLessons.length > 3 && (
										<div className="text-xs text-gray-500">
											+{dayLessons.length - 3} more
										</div>
									)}
								</div>
							</div>
						);
					})}
				</div>
			</div>
		);
	};

	const formatRange = `${format(startDate, "MMM d")} – ${format(
		endDate,
		"d, yyyy"
	)}`;

	// Mouse event handlers removed with hover functionality

	// getHoverDateTime function removed - was for tooltip functionality that's not implemented

	// Remove unused destructuring - getHoverDateTime() was for tooltip functionality

	const handleLessonClick = (lesson) => {
		setSelectedLesson(lesson);
	};

	const handleReschedule = () => {
		setShowReschedule(true);
	};

	const handleScheduleTutor = (tutor) => {
		console.log("Scheduling with:", tutor);
		setShowSchedulePopup(false);
	};

	const handleCancel = () => {
		setShowCancelConfirm(true);
	};

	const handleSchedule = (dayIdx, hour, slotIdx, weekStartDate) => {
		const date = addDays(weekStartDate, dayIdx);
		const startTime = new Date(date);
		startTime.setHours(hour, slotIdx * slotDuration);
		const endTime = new Date(startTime);
		endTime.setMinutes(endTime.getMinutes() + slotDuration);
		setSelectedSlot({
			dayIdx,
			hour,
			slotIdx,
			dateLabel: format(date, "EEEE, MMM d, yyyy"),
			timeLabel: `${format(startTime, "h:mm a")} – ${format(
				endTime,
				"h:mm a"
			)}`,
		});
		setShowSchedulePopup(true);
	};

	const closeReschedule = () => {
		setShowReschedule(false);
	};

	const closePopup = () => {
		setSelectedLesson(null);
		setShowReschedule(false);
		setShowCancelConfirm(false);
		setShowSchedulePopup(false);
		setSelectedSlot(null);
	};

	// hasLesson function removed - functionality is covered by getLessonForSlot

	// Get lesson for a specific slot (for week view)
	const getLessonForSlot = (dayIdx, hour, slotIdx, weekStartDate) => {
		return lessons.find((lesson) => {
			const slotDate = addDays(weekStartDate, dayIdx);

			if (!isSameDay(lesson.date, slotDate)) return false;
			if (lesson.time !== hour) return false;
			if (slotIdx !== Math.floor(lesson.minutes / slotDuration)) return false;

			return true;
		});
	};

	return (
		<div className="w-full relative">
			{/* View Controls and Filters */}
			<div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-2 bg-white p-2 rounded-lg ">
				{/* View Toggle Buttons */}
				<div className="flex items-center gap-2">




					<span className="text-sm font-medium text-gray-700 mr-2"></span>
					<div className="flex bg-gray-100 rounded-lg p-1">
						{Object.values(VIEW_TYPES).map((view) => (
							<button
								key={view}
								onClick={() => setCurrentView(view)}
								className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
									currentView === view
										? 'bg-white text-primary shadow-sm transform scale-105'
										: 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
								}`}
							>
								{view.charAt(0).toUpperCase() + view.slice(1)}
							</button>
						))}
					</div>
				</div>

				{/* Filters and Controls */}
				<div className="flex flex-wrap items-center gap-3">
					<span className="text-sm font-medium text-gray-700">Filters:</span>

					{/* Status Filter */}
					<div className="relative">
						<select
							value={statusFilter}
							onChange={(e) => setStatusFilter(e.target.value)}
							className="appearance-none border border-gray-300 rounded-md px-3 py-2 pr-8 text-sm bg-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 hover:border-gray-400"
						>
							<option value="all">All Status</option>
							<option value="scheduled">Scheduled</option>
							<option value="confirmed">Confirmed</option>
							<option value="completed">Completed</option>
							<option value="cancelled">Cancelled</option>
							<option value="pending">Pending</option>
						</select>
						<div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
							<svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
							</svg>
						</div>
					</div>

					{/* Tutor Filter */}
					<div className="relative">
						<select
							value={tutorFilter}
							onChange={(e) => setTutorFilter(e.target.value)}
							className="appearance-none border border-gray-300 rounded-md px-3 py-2 pr-8 text-sm bg-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 hover:border-gray-400"
						>
							<option value="all">All Tutors</option>
							{uniqueTutors.map((tutor) => (
								<option key={tutor.id} value={tutor.id}>
									{tutor.name}
								</option>
							))}
						</select>
						<div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
							<svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
							</svg>
						</div>
					</div>

					{/* Sort Options */}
					<div className="relative">
						<select
							value={`${sortBy}-${sortOrder}`}
							onChange={(e) => {
								const [newSortBy, newSortOrder] = e.target.value.split('-');
								setSortBy(newSortBy);
								setSortOrder(newSortOrder);
							}}
							className="appearance-none border border-gray-300 rounded-md px-3 py-2 pr-8 text-sm bg-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 hover:border-gray-400"
						>
							<option value="date-asc">Date (Earliest)</option>
							<option value="date-desc">Date (Latest)</option>
							<option value="tutor-asc">Tutor (A-Z)</option>
							<option value="tutor-desc">Tutor (Z-A)</option>
							<option value="status-asc">Status (A-Z)</option>
							<option value="status-desc">Status (Z-A)</option>
						</select>
						<div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
							<svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
							</svg>
						</div>
					</div>

					{/* Clear Filters Button */}
					{(statusFilter !== 'all' || tutorFilter !== 'all' || sortBy !== 'date' || sortOrder !== 'asc') && (
						<button
							onClick={() => {
								setStatusFilter('all');
								setTutorFilter('all');
								setSortBy('date');
								setSortOrder('asc');
							}}
							className="text-sm text-gray-500 hover:text-gray-700 underline transition-colors duration-200"
						>
							Clear Filters
						</button>
					)}
				</div>
			</div>

			{/* Navigation Controls (for Week and Month views) */}
			{currentView !== VIEW_TYPES.LIST && (
				<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-1 bg-white p-1 rounded-lg ">
					<div className="flex items-center gap-3">
						<button
							onClick={handleToday}
							className="border border-primary text-primary px-4 py-2 rounded-lg hover:bg-primary hover:text-white transition-all duration-200 font-medium"
						>
							Today
						</button>
						<div className="flex items-center gap-1">
							<button
								onClick={handlePrevious}
								className="bg-white border border-gray-300 p-2 rounded-lg hover:bg-gray-50 hover:border-gray-400 transition-all duration-200"
								title="Previous"
							>
								<img src={leftArrow} alt="prev" className="w-4 h-4" />
							</button>
							<button
								onClick={handleNext}
								className="bg-white border border-gray-300 p-2 rounded-lg hover:bg-gray-50 hover:border-gray-400 transition-all duration-200"
								title="Next"
							>
								<img src={rightArrow} alt="next" className="w-4 h-4" />
							</button>
						</div>
					</div>
					<h2 className="text-xl sm:text-2xl font-bold text-gray-900">{getDateRangeText()}</h2>
				</div>
			)}

			{/* Render Current View */}
			{currentView === VIEW_TYPES.LIST && <ListView />}
			{currentView === VIEW_TYPES.MONTH && <MonthView />}
			{currentView === VIEW_TYPES.WEEK && (
				<div>
					{/* Week View - existing implementation */}
					{(() => {
						const currentDate = new Date();
						const startDate = addWeeks(startOfWeek(currentDate, { weekStartsOn: 0 }), weekOffset);
						const endDate = addDays(startDate, 6);

						return (
							<>
								{/* Header */}
								<div className="grid grid-cols-8 text-sm font-medium border-b">
									<div></div>
									{DAYS.map((_, i) => (
										<div key={i} className="pl-2 py-2 border-l">
											<p className="text-[#71717A]">
												{format(addDays(startDate, i), "EEE")}
											</p>
											<p className="text-black">{format(addDays(startDate, i), "dd")}</p>
										</div>
									))}
								</div>

			{/* Body */}
			<div className="grid grid-cols-8">
				{/* Time Labels */}
				<div className="flex flex-col">
					{HOURS.flatMap((hour) =>
						Array.from({ length: SLOTS_PER_HOUR }).map((_, slotIdx) => (
							<div
								key={`${hour}-${slotIdx}`}
								className="text-xs text-gray-600 flex items-start"
								style={{ height: slotHeight }}
							>
								{slotIdx === 0 && `${hour}:00`}{" "}
								{/* Display as "0:00" to "23:00" */}
							</div>
						))
					)}
				</div>

				{/* Slots */}
				{DAYS.map((_, dayIdx) => (
					<div key={dayIdx} className="flex flex-col border-l relative">
						{HOURS.flatMap((hour) =>
							Array.from({ length: SLOTS_PER_HOUR }).map((_, slotIdx) => {
								const key = `${dayIdx}-${hour}-${slotIdx}`;
								const lesson = getLessonForSlot(dayIdx, hour, slotIdx, startDate);
								const isLessonStart =
									!!lesson &&
									slotIdx === Math.floor(lesson.minutes / slotDuration);

								return (
									<div
										key={key}
										className={`relative border-b h-[1px] ${
											isLessonStart
												? "bg-[#0EA5E91A] border-l-2 border-l-[#0EA5E9]"
												: ""
										}`}
										style={{ height: slotHeight }}
										// Hover functionality removed for now - can be re-implemented with tooltip later
										onClick={() => {
											if (lesson) {
												handleLessonClick(lesson);
											} else {
												handleSchedule(dayIdx, hour, slotIdx, startDate);
											}
										}}
									>
										{/* Lesson block */}
										{isLessonStart && (
											<div className="absolute inset-1 bg-sky-100 text-[8px] text-[#0369A1] rounded whitespace-pre-line z-10">
												<p>{lesson.status}</p>

												<p className="font-medium">{lesson.title}</p>
												<p>with {lesson.tutor}</p>
											</div>
										)}
									</div>
								);
							})
						)}
					</div>
				))}
			</div>
							</>
						);
					})()}
				</div>
			)}

			{/* Lesson Information modal */}
			{selectedLesson && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
					<LessonInfoModal
						selectedLesson={selectedLesson}
						onClose={closePopup}
						onReschedule={handleReschedule}
						onCancel={handleCancel}
					/>
				</div>
			)}

			{/* Reschedule Popup */}
			{showReschedule && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
					<RescheduleModal
						show={showReschedule}
						lesson={selectedLesson}
						onClose={closeReschedule}
					/>
				</div>
			)}

			{/* Cancel Confirmation Popup */}
			{showCancelConfirm && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
					<CancelConfirmationModal
						onCancelBookingConfirm={onCancelBookingConfirm}
						onCancel={() => setShowCancelConfirm(false)}
					/>
				</div>
			)}

			{/* Schedule Popup */}
			{showSchedulePopup && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
					<ScheduleTutorModal
						selectedSlot={selectedSlot}
						onClose={() => setShowSchedulePopup(false)}
						onScheduleTutor={handleScheduleTutor}
					/>
				</div>
			)}
		</div>
	);
}
